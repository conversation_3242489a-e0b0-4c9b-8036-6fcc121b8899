@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Deedy CV Template Styles */
@layer components {
  .deedy-resume {
    @apply max-w-5xl mx-auto bg-white p-10 font-sans text-gray-900 leading-relaxed;
    font-family: 'Lato', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 15px;
    line-height: 1.6;
  }

  .deedy-header {
    @apply mb-10 pb-8 border-b-2 border-gray-200;
  }

  .deedy-name {
    @apply text-5xl font-bold mb-3 text-gray-900 tracking-wide;
    font-family: 'Lato', sans-serif;
    font-weight: 700;
  }

  .deedy-title {
    @apply text-2xl font-medium text-gray-700 mb-6;
    font-family: 'Lato', sans-serif;
    font-weight: 400;
  }

  .deedy-contact {
    @apply flex flex-wrap justify-center gap-4 text-base text-gray-600;
  }

  .deedy-contact-item {
    @apply inline-flex items-center gap-1 px-3 py-1 bg-gray-50 rounded-full border border-gray-200 hover:bg-gray-100 transition-colors;
  }

  .deedy-contact-item a {
    @apply text-inherit hover:text-blue-600;
  }

  .deedy-columns {
    @apply grid grid-cols-1 lg:grid-cols-3 gap-10;
  }

  .deedy-left-column {
    @apply lg:col-span-1 space-y-8;
  }

  .deedy-right-column {
    @apply lg:col-span-2 space-y-8;
  }

  .deedy-section {
    @apply mb-8;
  }

  .deedy-section-header {
    @apply text-xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-gray-300 uppercase tracking-wide;
    font-family: 'Lato', sans-serif;
    font-weight: 700;
    font-size: 1.1rem;
    letter-spacing: 0.1em;
  }

  .deedy-subsection-header {
    @apply text-lg font-semibold text-gray-800 mb-3 mt-5;
    font-family: 'Lato', sans-serif;
    font-weight: 600;
  }

  .deedy-section-content {
    @apply space-y-4;
  }

  /* Enhanced section content for better organization */
  .deedy-left-column .deedy-section-content {
    @apply space-y-3;
  }

  .deedy-right-column .deedy-section-content {
    @apply space-y-5;
  }

  /* Company name styling */
  .deedy-company-name {
    @apply font-bold text-blue-900 text-lg;
    font-weight: 700;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced experience entry styling */
  .deedy-experience-entry {
    @apply mb-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border-l-4 border-blue-500;
  }

  .deedy-experience-header {
    @apply flex justify-between items-start mb-2;
  }

  .deedy-position-company {
    @apply flex items-center gap-2 flex-wrap;
  }

  .deedy-position {
    @apply font-semibold text-gray-900 text-base;
  }

  .deedy-company-separator {
    @apply text-blue-600 font-bold text-lg;
  }

  .deedy-dates {
    @apply text-sm text-gray-600 font-medium bg-white px-3 py-1 rounded-full border border-gray-200;
  }

  /* Enhanced bullet point styling */
  .deedy-bullet-item {
    @apply flex items-start mb-3 p-2 rounded transition-colors;
  }

  .deedy-bullet-item:hover {
    @apply bg-gray-50;
  }

  .deedy-bullet-point {
    @apply flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-3 mr-4;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  .deedy-bullet-content {
    @apply text-gray-700 text-base leading-relaxed flex-1;
    font-size: 15px;
  }

  /* Text content styling */
  .deedy-text-content {
    @apply text-gray-700 text-base leading-relaxed mb-3;
    font-size: 15px;
  }

  /* Enhanced link styling for better alignment */
  .deedy-text-content a,
  .deedy-bullet-content a {
    @apply inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 font-medium;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;
    padding: 2px 4px;
    border-radius: 4px;
  }

  .deedy-text-content a:hover,
  .deedy-bullet-content a:hover {
    @apply bg-blue-50;
    border-bottom-color: currentColor;
  }

  /* Special styling for different link types */
  .deedy-text-content a[href^="mailto:"],
  .deedy-bullet-content a[href^="mailto:"] {
    @apply text-green-600 hover:text-green-800 hover:bg-green-50;
  }

  .deedy-text-content a[href^="tel:"],
  .deedy-bullet-content a[href^="tel:"] {
    @apply text-purple-600 hover:text-purple-800 hover:bg-purple-50;
  }

  .deedy-text-content a[href*="linkedin"],
  .deedy-bullet-content a[href*="linkedin"] {
    @apply text-blue-700 hover:text-blue-900 hover:bg-blue-50;
  }

  .deedy-text-content a[href*="github"],
  .deedy-bullet-content a[href*="github"] {
    @apply text-gray-700 hover:text-gray-900 hover:bg-gray-50;
  }

  /* Enhanced skills section styling */
  .deedy-skill-category {
    @apply mb-4;
  }

  .deedy-skill-category-title {
    @apply text-sm font-bold text-gray-800 mb-2 uppercase tracking-wide;
    font-family: 'Lato', sans-serif;
  }

  .deedy-skill-tags {
    @apply flex flex-wrap gap-2;
  }

  .deedy-skill-tag {
    @apply inline-block bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium border border-blue-200 hover:from-blue-200 hover:to-indigo-200 transition-all;
    font-size: 13px;
  }

  /* Highlighting styles */
  .deedy-tech-highlight {
    @apply inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-md text-sm font-semibold mr-2 mb-1;
    font-size: 13px;
  }

  .deedy-keyword-highlight {
    @apply inline-block bg-green-100 text-green-800 px-3 py-1 rounded-md text-sm font-semibold mr-2 mb-1;
    font-size: 13px;
  }

  .deedy-metric-highlight {
    @apply inline-block bg-orange-100 text-orange-800 px-3 py-1 rounded-md text-sm font-semibold mr-2 mb-1;
    font-size: 13px;
  }

  /* Print styles for ATS compatibility */
  @media print {
    .deedy-resume {
      @apply p-0 max-w-none;
      font-size: 12pt;
      line-height: 1.5;
    }

    .deedy-columns {
      @apply grid-cols-3;
    }

    .deedy-tech-highlight,
    .deedy-keyword-highlight,
    .deedy-metric-highlight {
      @apply bg-transparent text-gray-900 px-0 py-0 rounded-none font-normal;
    }

    .deedy-section-header {
      @apply border-b border-gray-400;
    }

    .deedy-bullet-item {
      @apply hover:bg-transparent p-0;
    }

    .deedy-company-name {
      @apply text-base;
    }
  }

  /* Responsive adjustments */
  @media (max-width: 1024px) {
    .deedy-columns {
      @apply grid-cols-1;
    }

    .deedy-left-column,
    .deedy-right-column {
      @apply col-span-1;
    }

    .deedy-name {
      @apply text-4xl;
    }

    .deedy-title {
      @apply text-xl;
    }

    .deedy-resume {
      @apply p-6;
      font-size: 14px;
    }

    .deedy-section-header {
      @apply text-lg;
    }

    .deedy-bullet-content,
    .deedy-text-content {
      font-size: 14px;
    }
  }

  @media (max-width: 768px) {
    .deedy-name {
      @apply text-3xl;
    }

    .deedy-title {
      @apply text-lg;
    }

    .deedy-resume {
      @apply p-4;
      font-size: 13px;
    }

    .deedy-contact {
      @apply gap-2 text-sm flex-col items-center;
    }

    .deedy-contact-item {
      @apply px-2 py-1 text-sm;
    }

    .deedy-bullet-content,
    .deedy-text-content {
      font-size: 13px;
    }

    .deedy-experience-header {
      @apply flex-col items-start gap-2;
    }

    .deedy-position-company {
      @apply flex-col items-start gap-1;
    }

    .deedy-company-name {
      @apply text-base;
    }

    .deedy-skill-tags {
      @apply gap-1;
    }

    .deedy-skill-tag {
      @apply px-2 py-1 text-xs;
    }
  }

  /* Enhanced typography for better readability */
  .deedy-resume p {
    @apply text-sm leading-relaxed;
  }

  .deedy-resume ul {
    @apply list-none;
  }

  .deedy-resume li {
    @apply mb-1;
  }

  /* Better link styling */
  .deedy-resume a {
    @apply text-blue-600 hover:text-blue-800 no-underline;
    transition: color 0.2s ease;
  }

  .deedy-resume a:hover {
    @apply underline;
  }

  /* Improved spacing for better visual hierarchy */
  .deedy-section:last-child {
    @apply mb-0;
  }

  .deedy-section-content > *:last-child {
    @apply mb-0;
  }

  /* Better bullet point styling */
  .deedy-section-content .flex {
    @apply items-start;
  }

  .deedy-section-content .flex > div:first-child {
    @apply mt-2;
  }

  /* Enhanced highlighting for better contrast */
  .deedy-tech-highlight {
    @apply bg-blue-50 text-blue-900 border border-blue-200;
    font-size: 13px;
  }

  .deedy-keyword-highlight {
    @apply bg-green-50 text-green-900 border border-green-200;
    font-size: 13px;
  }

  .deedy-metric-highlight {
    @apply bg-orange-50 text-orange-900 border border-orange-200;
    font-size: 13px;
  }

  /* Additional enhancements */
  .deedy-resume strong {
    @apply font-bold text-gray-900;
  }

  .deedy-resume em {
    @apply italic text-gray-700;
  }

  /* Hover effects for interactive elements */
  .deedy-bullet-item:hover .deedy-bullet-point {
    @apply bg-blue-700;
    transform: scale(1.1);
    transition: all 0.2s ease;
  }

  .deedy-bullet-item:hover .deedy-bullet-content {
    @apply text-gray-900;
  }

  /* Better spacing for sections */
  .deedy-section:first-child {
    @apply mt-0;
  }

  .deedy-section:last-child {
    @apply mb-0;
  }

  /* Enhanced typography for better readability */
  .deedy-resume h1, .deedy-resume h2, .deedy-resume h3, .deedy-resume h4 {
    @apply font-bold;
    font-family: 'Lato', sans-serif;
  }

  .deedy-resume p {
    @apply leading-relaxed;
  }

  /* Better link styling within content */
  .deedy-bullet-content a,
  .deedy-text-content a {
    @apply text-blue-600 hover:text-blue-800 font-medium;
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: border-color 0.2s ease;
  }

  .deedy-bullet-content a:hover,
  .deedy-text-content a:hover {
    border-bottom-color: currentColor;
  }

  /* Cover Letter Styles */
  .deedy-cover-letter {
    @apply max-w-4xl mx-auto bg-white p-10 font-sans text-gray-900 leading-relaxed;
    font-family: 'Lato', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 15px;
    line-height: 1.6;
  }

  .deedy-cl-header {
    @apply mb-8 pb-6 border-b border-gray-200 flex justify-between items-start;
  }

  .deedy-cl-sender {
    @apply flex-1;
  }

  .deedy-cl-name {
    @apply text-3xl font-bold mb-3 text-gray-900;
    font-family: 'Lato', sans-serif;
    font-weight: 700;
  }

  .deedy-cl-address {
    @apply text-base text-gray-700 mb-1;
  }

  .deedy-cl-contact {
    @apply flex flex-wrap gap-4 mt-3 text-base text-gray-600;
  }

  .deedy-cl-contact-item {
    @apply inline-flex items-center;
  }

  .deedy-cl-date {
    @apply text-base text-gray-700 font-medium;
  }

  .deedy-cl-recipient {
    @apply mb-8 pb-4;
  }

  .deedy-cl-recipient-name {
    @apply text-lg font-semibold text-gray-900 mb-1;
  }

  .deedy-cl-recipient-title {
    @apply text-base text-gray-700 mb-1;
  }

  .deedy-cl-recipient-company {
    @apply text-lg font-bold text-gray-900 mb-2;
  }

  .deedy-cl-recipient-address {
    @apply text-base text-gray-700 mb-1;
  }

  .deedy-cl-body {
    @apply space-y-6;
  }

  .deedy-cl-salutation {
    @apply text-base font-medium text-gray-900 mb-6;
  }

  .deedy-cl-content {
    @apply space-y-4;
  }

  .deedy-cl-paragraph {
    @apply text-base leading-relaxed text-gray-700 mb-4;
    text-align: justify;
  }

  .deedy-cl-closing {
    @apply text-base font-medium text-gray-900 mt-8 mb-4;
  }

  .deedy-cl-signature {
    @apply text-lg font-bold text-gray-900 mt-4;
  }

  /* Print styles for cover letter */
  @media print {
    .deedy-cover-letter {
      @apply p-0 max-w-none;
      font-size: 12pt;
      line-height: 1.5;
    }

    .deedy-cl-header {
      @apply border-b border-gray-400;
    }

    .deedy-cl-name {
      @apply text-2xl;
    }

    .deedy-cl-recipient-company {
      @apply text-base;
    }
  }

  /* Responsive adjustments for cover letter */
  @media (max-width: 1024px) {
    .deedy-cover-letter {
      @apply p-6;
      font-size: 14px;
    }

    .deedy-cl-name {
      @apply text-2xl;
    }

    .deedy-cl-header {
      @apply flex-col items-start gap-4;
    }
  }

  @media (max-width: 768px) {
    .deedy-cover-letter {
      @apply p-4;
      font-size: 13px;
    }

    .deedy-cl-name {
      @apply text-xl;
    }

    .deedy-cl-contact {
      @apply gap-2 text-sm flex-col items-start;
    }
  }
}