// Sample data for testing the applications functionality
export const sampleApplications = [
  {
    id: "1701234567890",
    jobTitle: "Senior Full Stack Developer",
    company: "TechCorp Inc",
    jobDescription: `Senior Full Stack Developer - TechCorp Inc

We are looking for a Senior Full Stack Developer to join our growing team. 

Requirements:
- 5+ years of experience with React, Node.js, and TypeScript
- Experience with cloud platforms (AWS, Azure)
- Strong knowledge of databases (PostgreSQL, MongoDB)
- Experience with CI/CD pipelines
- Excellent communication skills

Responsibilities:
- Develop and maintain web applications
- Collaborate with cross-functional teams
- Mentor junior developers
- Participate in code reviews

Location: San Francisco, CA (Remote friendly)
Salary: $120,000 - $160,000`,
    resume: `BHUPENDER SHARMA
Full Stack Developer

CONTACT INFORMATION
📧 <EMAIL>
📱 +91-9876543210
🔗 linkedin.com/in/bhupender
💻 github.com/bhupenderkumar

PROFESSIONAL SUMMARY
Senior Full Stack Developer with 6+ years of experience building scalable web applications using React, Node.js, and TypeScript. Proven track record of delivering high-quality solutions in cloud environments with expertise in AWS and modern development practices.

TECHNICAL SKILLS
• Frontend: React, TypeScript, JavaScript, HTML5, CSS3, Tailwind CSS
• Backend: Node.js, Express.js, Python, REST APIs, GraphQL
• Databases: PostgreSQL, MongoDB, Redis
• Cloud: AWS (EC2, S3, Lambda, RDS), Docker, Kubernetes
• Tools: Git, Jenkins, CI/CD, Jest, Webpack

PROFESSIONAL EXPERIENCE

Senior Software Engineer | TechSolutions Ltd | 2021 - Present
• Developed and maintained 15+ React applications serving 100K+ users
• Implemented microservices architecture reducing system latency by 40%
• Led a team of 4 developers and mentored 2 junior developers
• Established CI/CD pipelines improving deployment frequency by 60%

Full Stack Developer | StartupXYZ | 2019 - 2021
• Built responsive web applications using React and Node.js
• Optimized database queries resulting in 50% performance improvement
• Collaborated with product team to deliver features on tight deadlines
• Implemented automated testing increasing code coverage to 85%

EDUCATION
Bachelor of Technology in Computer Science
Indian Institute of Technology | 2015 - 2019

CERTIFICATIONS
• AWS Certified Solutions Architect
• MongoDB Certified Developer`,
    coverLetter: `Dear Hiring Manager,

I am writing to express my strong interest in the Senior Full Stack Developer position at TechCorp Inc. With over 6 years of experience in full-stack development and a proven track record of building scalable applications, I am excited about the opportunity to contribute to your growing team.

Your job posting particularly caught my attention because of the emphasis on React, Node.js, and TypeScript - technologies I have been working with extensively throughout my career. In my current role at TechSolutions Ltd, I have successfully developed and maintained 15+ React applications serving over 100,000 users, while implementing microservices architecture that reduced system latency by 40%.

What makes me a strong fit for this role:

• Technical Expertise: 6+ years of hands-on experience with React, Node.js, and TypeScript, along with extensive knowledge of cloud platforms including AWS
• Leadership Experience: Currently leading a team of 4 developers and have mentored 2 junior developers, aligning perfectly with your mentorship requirements
• Database Proficiency: Strong experience with both PostgreSQL and MongoDB, having optimized database queries to achieve 50% performance improvements
• CI/CD Experience: Established CI/CD pipelines that improved deployment frequency by 60%

I am particularly drawn to TechCorp Inc's reputation for innovation and your commitment to using cutting-edge technologies. The remote-friendly culture aligns well with my preference for flexible work arrangements while maintaining high productivity and collaboration standards.

I would welcome the opportunity to discuss how my technical skills and leadership experience can contribute to TechCorp Inc's continued success. Thank you for considering my application.

Best regards,
Bhupender Sharma`,
    email: `Subject: Application for Senior Full Stack Developer Position - Bhupender Sharma

Dear TechCorp Inc Hiring Team,

I hope this email finds you well. I am writing to submit my application for the Senior Full Stack Developer position posted on your careers page.

With 6+ years of experience in full-stack development specializing in React, Node.js, and TypeScript, I am confident that my technical expertise and leadership experience make me an ideal candidate for this role.

Key highlights of my background:
• Led development of 15+ React applications serving 100K+ users
• Implemented microservices architecture reducing system latency by 40%
• Extensive experience with AWS, PostgreSQL, and MongoDB
• Proven track record in mentoring developers and establishing CI/CD pipelines

I have attached my resume and cover letter for your review. I am excited about the opportunity to contribute to TechCorp Inc's innovative projects and would welcome the chance to discuss how my skills align with your team's needs.

I am available for an interview at your convenience and can start immediately if selected.

Thank you for your time and consideration.

Best regards,
Bhupender Sharma
📧 <EMAIL>
📱 +91-9876543210
🔗 linkedin.com/in/bhupender
💻 github.com/bhupenderkumar`,
    language: "en",
    country: "International",
    date: "12/6/2024",
    timestamp: "2024-12-06T10:30:00.000Z",
    version: 1
  },
  {
    id: "1701234567891",
    jobTitle: "React Developer",
    company: "StartupHub",
    jobDescription: `React Developer - StartupHub

Join our dynamic startup as a React Developer!

Requirements:
- 3+ years React experience
- TypeScript knowledge
- Experience with state management (Redux/Zustand)
- Familiarity with testing frameworks
- Agile development experience

What we offer:
- Competitive salary
- Equity options
- Flexible working hours
- Learning budget

Location: Remote
Salary: $80,000 - $100,000`,
    resume: `BHUPENDER SHARMA
React Developer

CONTACT INFORMATION
📧 <EMAIL>
📱 +91-9876543210
🔗 linkedin.com/in/bhupender
💻 github.com/bhupenderkumar

PROFESSIONAL SUMMARY
Passionate React Developer with 4+ years of experience building modern, responsive web applications. Expertise in TypeScript, state management, and testing frameworks with a strong focus on user experience and performance optimization.

TECHNICAL SKILLS
• Frontend: React, TypeScript, JavaScript, HTML5, CSS3, Tailwind CSS
• State Management: Redux, Zustand, Context API
• Testing: Jest, React Testing Library, Cypress
• Tools: Git, Webpack, Vite, ESLint, Prettier
• Methodologies: Agile, Scrum, Test-Driven Development

PROFESSIONAL EXPERIENCE

Frontend Developer | WebSolutions Co | 2020 - Present
• Developed 10+ React applications with TypeScript
• Implemented Redux for complex state management
• Achieved 95% test coverage using Jest and React Testing Library
• Collaborated in Agile sprints with cross-functional teams

Junior React Developer | DigitalCraft | 2019 - 2020
• Built responsive components using React and CSS3
• Participated in code reviews and pair programming
• Learned and applied modern React patterns and hooks

EDUCATION
Bachelor of Technology in Computer Science
Indian Institute of Technology | 2015 - 2019

PROJECTS
• E-commerce Platform: React + TypeScript + Redux
• Task Management App: React + Zustand + Testing Library`,
    coverLetter: `Dear StartupHub Team,

I am excited to apply for the React Developer position at StartupHub. As a passionate React developer with 4+ years of experience, I am drawn to your startup's innovative approach and dynamic environment.

My experience aligns perfectly with your requirements:
• 4+ years of React development with TypeScript
• Extensive experience with Redux and Zustand for state management
• Strong testing background with Jest and React Testing Library
• Proven experience in Agile development environments

I am particularly excited about the opportunity to work in a startup environment where I can contribute to building innovative products while continuing to learn and grow.

Best regards,
Bhupender Sharma`,
    email: `Subject: Application for React Developer Position - Bhupender Sharma

Dear StartupHub Hiring Team,

I am writing to express my interest in the React Developer position at StartupHub.

With 4+ years of React development experience and expertise in TypeScript and state management, I am excited about the opportunity to contribute to your innovative startup.

Please find my resume and cover letter attached. I would love to discuss how my skills can help StartupHub achieve its goals.

Best regards,
Bhupender Sharma
📧 <EMAIL>`,
    language: "en",
    country: "International",
    date: "11/6/2024",
    timestamp: "2024-12-05T14:20:00.000Z",
    version: 2
  }
];

// Function to populate sample data for testing
export const populateSampleData = () => {
  try {
    const existingData = localStorage.getItem('applications');
    if (!existingData || JSON.parse(existingData).length === 0) {
      localStorage.setItem('applications', JSON.stringify(sampleApplications));
      console.log('Sample applications data populated');
      
      // Dispatch event to notify components
      window.dispatchEvent(new CustomEvent('applicationsUpdated'));
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error populating sample data:', error);
    return false;
  }
};

// Function to clear all application data
export const clearApplicationData = () => {
  try {
    localStorage.removeItem('applications');
    window.dispatchEvent(new CustomEvent('applicationsUpdated'));
    console.log('Application data cleared');
    return true;
  } catch (error) {
    console.error('Error clearing application data:', error);
    return false;
  }
};
