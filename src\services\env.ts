// --- <PERSON><PERSON><PERSON> SERVICE ---
// Centralized environment variable service for runtime and localStorage/universalStorage
import { universalStorage } from './universalStorage';

// Demo API key (your fallback key) - hidden from UI
// TODO: Replace this with your actual Gemini API key
const DEMO_GEMINI_API_KEY = 'YOUR_ACTUAL_GEMINI_API_KEY_HERE';

export const getUserEnvVar = (key: string): string | undefined => {
  // Try universalStorage (async, but we provide a sync fallback for legacy usage)
  // This function is now async for universalStorage support
  throw new Error('getUserEnvVar is now async. Use getUserEnvVarAsync instead.');
};

export const getUserEnvVarAsync = async (key: string): Promise<string | undefined> => {
  // Check if demo mode is enabled
  const isDemoMode = await universalStorage.get('DEMO_MODE_ENABLED');

  if (key === 'VITE_GEMINI_API_KEY' && isDemoMode === 'true') {
    return DEMO_GEMINI_API_KEY;
  }

  // Try universalStorage first
  const value = await universalStorage.get(key);
  if (value) return value;

  // Fallback to import.meta.env
  return import.meta.env[key];
};

export const setUserEnvVar = (key: string, value: string) => {
  // Legacy sync set (for compatibility)
  throw new Error('setUserEnvVar is now async. Use setUserEnvVarAsync instead.');
};

export const setUserEnvVarAsync = async (key: string, value: string) => {
  await universalStorage.set(key, value);
};

export const enableDemoMode = async () => {
  await universalStorage.set('DEMO_MODE_ENABLED', 'true');
};

export const disableDemoMode = async () => {
  await universalStorage.remove('DEMO_MODE_ENABLED');
};

export const isDemoModeEnabled = async (): Promise<boolean> => {
  const demoMode = await universalStorage.get('DEMO_MODE_ENABLED');
  return demoMode === 'true';
};