// Quick PDF generator for AI-generated resumes
import jsPDF from 'jspdf';

export interface ResumeData {
  content: string;
  jobTitle: string;
  company: string;
  applicantName?: string;
}

export function generateQuickResumePDF(resumeData: ResumeData): void {
  try {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 20;
    const maxWidth = pageWidth - 2 * margin;
    
    // Set up fonts and colors
    doc.setFont('helvetica');
    
    // Header
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    
    // Title
    const fileName = `Resume_${resumeData.company}_${resumeData.jobTitle}`.replace(/[^a-zA-Z0-9]/g, '_');
    doc.text(`Resume for ${resumeData.jobTitle}`, margin, 30);
    doc.setFontSize(12);
    doc.text(`${resumeData.company}`, margin, 45);
    
    // Add a line separator
    doc.setLineWidth(0.5);
    doc.line(margin, 55, pageWidth - margin, 55);
    
    // Content
    doc.setFontSize(10);
    doc.setTextColor(40, 40, 40);
    
    // Split content into lines and handle page breaks
    const lines = doc.splitTextToSize(resumeData.content, maxWidth);
    let yPosition = 70;
    const lineHeight = 6;
    
    lines.forEach((line: string) => {
      if (yPosition > pageHeight - margin) {
        doc.addPage();
        yPosition = margin;
      }
      
      doc.text(line, margin, yPosition);
      yPosition += lineHeight;
    });
    
    // Footer
    const footerY = pageHeight - 15;
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text('Generated by Career Jumpstart Hub - AI Resume Generator', margin, footerY);
    doc.text(`Generated on: ${new Date().toLocaleDateString()}`, pageWidth - margin - 60, footerY);
    
    // Save the PDF
    doc.save(`${fileName}.pdf`);
    
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Failed to generate PDF resume');
  }
}

// Enhanced PDF generator with better formatting
export function generateEnhancedResumePDF(resumeData: ResumeData): void {
  try {
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();
    const margin = 20;
    const maxWidth = pageWidth - 2 * margin;

    // Parse resume content into sections
    const sections = parseResumeContent(resumeData.content);
    const contactInfo = extractContactInformation(resumeData.content);

    let yPosition = 30;
    
    // Header with gradient-like effect
    doc.setFillColor(59, 130, 246); // Blue color
    doc.rect(0, 0, pageWidth, 35, 'F');

    // Name
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(20);
    doc.setFont('helvetica', 'bold');
    const name = contactInfo.name || sections.name || 'Professional Resume';
    doc.text(name, margin, 20);

    // Contact info in header
    doc.setFontSize(9);
    doc.setFont('helvetica', 'normal');
    let contactY = 28;
    let contactX = margin;

    if (contactInfo.email) {
      doc.text(`📧 ${contactInfo.email}`, contactX, contactY);
      contactX += doc.getTextWidth(`📧 ${contactInfo.email}`) + 15;
    }

    if (contactInfo.phone) {
      doc.text(`📱 ${contactInfo.phone}`, contactX, contactY);
      contactX += doc.getTextWidth(`📱 ${contactInfo.phone}`) + 15;
    }

    if (contactInfo.linkedin) {
      doc.text(`💼 ${contactInfo.linkedin}`, contactX, contactY);
      contactX += doc.getTextWidth(`💼 ${contactInfo.linkedin}`) + 15;
    }

    if (contactInfo.github) {
      doc.text(`💻 ${contactInfo.github}`, contactX, contactY);
    }

    // Reset colors
    doc.setTextColor(0, 0, 0);
    yPosition = 50;
    
    // Job target info
    doc.setFontSize(12);
    doc.setFont('helvetica', 'normal');
    doc.text(`Target Position: ${resumeData.jobTitle}`, margin, yPosition);
    yPosition += 8;
    doc.text(`Target Company: ${resumeData.company}`, margin, yPosition);
    yPosition += 15;
    
    // Process each section
    Object.entries(sections).forEach(([sectionName, content]) => {
      if (sectionName === 'name' || !content) return;
      
      // Check if we need a new page
      if (yPosition > pageHeight - 50) {
        doc.addPage();
        yPosition = 30;
      }
      
      // Section header
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(59, 130, 246);
      doc.text(sectionName.toUpperCase(), margin, yPosition);
      yPosition += 8;
      
      // Section underline
      doc.setLineWidth(0.5);
      doc.setDrawColor(59, 130, 246);
      doc.line(margin, yPosition - 2, margin + 50, yPosition - 2);
      yPosition += 5;
      
      // Section content
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.setTextColor(40, 40, 40);
      
      const lines = doc.splitTextToSize(content, maxWidth);
      lines.forEach((line: string) => {
        if (yPosition > pageHeight - margin) {
          doc.addPage();
          yPosition = margin;
        }
        doc.text(line, margin, yPosition);
        yPosition += 5;
      });
      
      yPosition += 10; // Space between sections
    });
    
    // Footer - minimal and clean
    const footerY = pageHeight - 10;
    doc.setFontSize(7);
    doc.setTextColor(150, 150, 150);
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, pageWidth - margin - 40, footerY);
    
    // Save with descriptive filename
    const fileName = `AI_Resume_${resumeData.company}_${resumeData.jobTitle}_${new Date().toISOString().split('T')[0]}`.replace(/[^a-zA-Z0-9_-]/g, '_');
    doc.save(`${fileName}.pdf`);
    
  } catch (error) {
    console.error('Error generating enhanced PDF:', error);
    // Fallback to simple PDF
    generateQuickResumePDF(resumeData);
  }
}

// Parse resume content into sections
function parseResumeContent(content: string): Record<string, string> {
  const sections: Record<string, string> = {};
  
  // Common section headers
  const sectionHeaders = [
    'name', 'contact', 'summary', 'objective', 'experience', 'work experience',
    'education', 'skills', 'technical skills', 'projects', 'certifications',
    'achievements', 'awards', 'languages', 'interests', 'references'
  ];
  
  let currentSection = 'summary';
  let currentContent = '';
  
  const lines = content.split('\n');
  
  lines.forEach(line => {
    const lowerLine = line.toLowerCase().trim();
    
    // Check if this line is a section header
    const matchedHeader = sectionHeaders.find(header => 
      lowerLine.includes(header) && lowerLine.length < 50
    );
    
    if (matchedHeader) {
      // Save previous section
      if (currentContent.trim()) {
        sections[currentSection] = currentContent.trim();
      }
      
      // Start new section
      currentSection = matchedHeader;
      currentContent = '';
    } else {
      // Add to current section
      currentContent += line + '\n';
    }
  });
  
  // Save the last section
  if (currentContent.trim()) {
    sections[currentSection] = currentContent.trim();
  }
  
  return sections;
}

// Extract contact information from resume content
function extractContactInformation(content: string): any {
  const lines = content.split('\n');
  const contactInfo: any = {};

  // Extract name (usually first line)
  if (lines.length > 0) {
    const firstLine = lines[0].replace(/\*\*/g, '').replace(/[#*]/g, '').trim();
    if (firstLine && !firstLine.includes('@') && !firstLine.includes('+') && !firstLine.includes('http')) {
      contactInfo.name = firstLine;
    }
  }

  // Extract contact details
  lines.forEach(line => {
    const cleanLine = line.trim();

    // Email
    const emailMatch = line.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
    if (emailMatch) {
      contactInfo.email = emailMatch[1];
    }

    // Phone
    const phoneMatch = line.match(/(\+?[\d\s\-\(\)]{10,})/);
    if (phoneMatch && (cleanLine.toLowerCase().includes('phone') || cleanLine.includes('📱') || cleanLine.includes('☎') || cleanLine.includes('+'))) {
      contactInfo.phone = phoneMatch[1].trim();
    }

    // LinkedIn
    if (cleanLine.toLowerCase().includes('linkedin')) {
      const linkedinMatch = line.match(/(linkedin\.com\/in\/[a-zA-Z0-9\-]+)/i);
      if (linkedinMatch) {
        contactInfo.linkedin = linkedinMatch[1];
      } else {
        // Try to extract just the profile part
        const profileMatch = line.match(/linkedin\.com\/in\/([a-zA-Z0-9\-]+)/i);
        if (profileMatch) {
          contactInfo.linkedin = `linkedin.com/in/${profileMatch[1]}`;
        }
      }
    }

    // GitHub
    if (cleanLine.toLowerCase().includes('github')) {
      const githubMatch = line.match(/(github\.com\/[a-zA-Z0-9\-]+)/i);
      if (githubMatch) {
        contactInfo.github = githubMatch[1];
      }
    }

    // Location
    if (cleanLine.toLowerCase().includes('location') || cleanLine.includes('📍')) {
      const locationMatch = line.match(/(?:location:?\s*|📍\s*)([a-zA-Z\s,]+)/i);
      if (locationMatch) {
        contactInfo.location = locationMatch[1].trim();
      }
    }
  });

  return contactInfo;
}

// Quick download function for text format
export function downloadResumeAsText(content: string, jobTitle: string, company: string): void {
  const element = document.createElement("a");
  const file = new Blob([content], { type: 'text/plain' });
  element.href = URL.createObjectURL(file);
  element.download = `Resume_${company}_${jobTitle}_${new Date().toISOString().split('T')[0]}.txt`.replace(/[^a-zA-Z0-9_.-]/g, '_');
  document.body.appendChild(element);
  element.click();
  document.body.removeChild(element);
}
